import React, { useState } from 'react';
import { UniversalChat } from '@/components/chat';
import { ChatProvider } from '@/contexts/ChatContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * Chat Test Page
 * 
 * This page allows testing the Universal Chat component with different roles and variants.
 * Useful for development and debugging.
 */
const ChatTest: React.FC = () => {
  const [currentRole, setCurrentRole] = useState<'admin' | 'provider' | 'customer'>('customer');
  const [currentVariant, setCurrentVariant] = useState<'compact' | 'full' | 'modal'>('full');
  const [showModal, setShowModal] = useState(false);

  const roles = [
    { value: 'admin' as const, label: 'Admin', color: 'bg-blue-500' },
    { value: 'provider' as const, label: 'Provider', color: 'bg-green-500' },
    { value: 'customer' as const, label: 'Customer', color: 'bg-purple-500' },
  ];

  const variants = [
    { value: 'full' as const, label: 'Full' },
    { value: 'compact' as const, label: 'Compact' },
    { value: 'modal' as const, label: 'Modal' },
  ];

  const handleRoleChange = (role: 'admin' | 'provider' | 'customer') => {
    setCurrentRole(role);
  };

  const handleVariantChange = (variant: 'compact' | 'full' | 'modal') => {
    setCurrentVariant(variant);
    if (variant === 'modal') {
      setShowModal(true);
    }
  };

  return (
    <ChatProvider>
      <div className="min-h-screen bg-gray-50 p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Universal Chat Component Test
          </h1>
          <p className="text-gray-600">
            Test the chat component with different roles and variants
          </p>
        </div>

        {/* Controls */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Role Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                User Role
              </label>
              <div className="flex gap-2">
                {roles.map((role) => (
                  <Button
                    key={role.value}
                    variant={currentRole === role.value ? 'default' : 'outline'}
                    onClick={() => handleRoleChange(role.value)}
                    className="flex items-center gap-2"
                  >
                    <div className={`w-3 h-3 rounded-full ${role.color}`} />
                    {role.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Variant Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chat Variant
              </label>
              <div className="flex gap-2">
                {variants.map((variant) => (
                  <Button
                    key={variant.value}
                    variant={currentVariant === variant.value ? 'default' : 'outline'}
                    onClick={() => handleVariantChange(variant.value)}
                  >
                    {variant.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Current Configuration */}
            <div className="flex items-center gap-4 pt-4 border-t">
              <Badge variant="outline">
                Role: {currentRole}
              </Badge>
              <Badge variant="outline">
                Variant: {currentVariant}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Chat Component Display */}
        <Card className="h-[600px]">
          <CardHeader>
            <CardTitle>
              Chat Component - {currentRole} ({currentVariant})
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[calc(100%-80px)]">
            {currentVariant === 'modal' ? (
              <div className="flex items-center justify-center h-full">
                <Button onClick={() => setShowModal(true)}>
                  Open Modal Chat
                </Button>
              </div>
            ) : currentVariant === 'compact' ? (
              <div className="flex justify-center items-start pt-8">
                <UniversalChat
                  userRole={currentRole}
                  userId={`test-${currentRole}-123`}
                  variant="compact"
                  theme={currentRole}
                  onChatSelect={(chatId) => {
                    console.log(`${currentRole} selected chat:`, chatId);
                  }}
                  onMessageSent={(message) => {
                    console.log(`${currentRole} sent message:`, message);
                  }}
                  onError={(error) => {
                    console.error(`${currentRole} chat error:`, error);
                  }}
                />
              </div>
            ) : (
              <div className="h-full">
                <UniversalChat
                  userRole={currentRole}
                  userId={`test-${currentRole}-123`}
                  variant="full"
                  theme={currentRole}
                  onChatSelect={(chatId) => {
                    console.log(`${currentRole} selected chat:`, chatId);
                  }}
                  onMessageSent={(message) => {
                    console.log(`${currentRole} sent message:`, message);
                  }}
                  onError={(error) => {
                    console.error(`${currentRole} chat error:`, error);
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal Chat */}
        {showModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-2xl w-[800px] h-[600px] overflow-hidden">
              <UniversalChat
                userRole={currentRole}
                userId={`test-${currentRole}-123`}
                variant="modal"
                theme={currentRole}
                onChatSelect={(chatId) => {
                  console.log(`${currentRole} selected chat:`, chatId);
                }}
                onMessageSent={(message) => {
                  console.log(`${currentRole} sent message:`, message);
                }}
                onError={(error) => {
                  console.error(`${currentRole} chat error:`, error);
                  setShowModal(false);
                }}
              />
              <Button
                className="absolute top-4 right-4"
                variant="outline"
                onClick={() => setShowModal(false)}
              >
                Close
              </Button>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>Current Role:</strong> {currentRole}</p>
              <p><strong>Current Variant:</strong> {currentVariant}</p>
              <p><strong>User ID:</strong> test-{currentRole}-123</p>
              <p><strong>Theme:</strong> {currentRole}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </ChatProvider>
  );
};

export default ChatTest;

import React, { createContext, useContext, useReducer, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { chatService } from '@/services/chatService';
import { websocketService } from '@/services/websocketService';
import debounce from 'lodash/debounce';
import {
  Chat,
  Message,
  SendMessageRequest,
  ChatContextType,
  ChatState,
  ChatAction,
  ChatParticipant,
  WebSocketMessage,
  MessageSentMessage
} from '@/types/chat';
import {
  getChatFeatures,
  getChatTheme,
  UserRole
} from '@/components/chat/config/roleConfigurations';
import { mockChats, getMockMessagesForChat, getMockChatsForRole } from '@/components/chat/mockData';

// Enhanced initial state for universal chat
const initialState: ChatState = {
  currentUser: {
    id: '',
    name: '',
    type: 'customer',
  },
  activeChats: [],
  selectedChat: null,
  messages: [],
  userRole: 'customer',
  permissions: getChatFeatures('customer'),
  theme: getChatTheme('customer'),
  isLoading: true,
  isConnected: false,
  error: null,
  typingUsers: {},
  onlineUsers: [],
};

// Enhanced reducer for universal chat
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload };
    case 'SET_USER_ROLE':
      return {
        ...state,
        userRole: action.payload,
        permissions: getChatFeatures(action.payload),
        theme: getChatTheme(action.payload)
      };
    case 'SET_PERMISSIONS':
      return { ...state, permissions: action.payload };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'SET_CHATS':
      return { ...state, activeChats: action.payload };
    case 'ADD_CHAT':
      return { ...state, activeChats: [...state.activeChats, action.payload] };
    case 'UPDATE_CHAT':
      return {
        ...state,
        activeChats: state.activeChats.map(chat =>
          chat.id === action.payload.chatId
            ? { ...chat, ...action.payload.updates }
            : chat
        )
      };
    case 'REMOVE_CHAT':
      return {
        ...state,
        activeChats: state.activeChats.filter(chat => chat.id !== action.payload),
        selectedChat: state.selectedChat?.id === action.payload ? null : state.selectedChat
      };
    case 'SELECT_CHAT':
      const selectedChat = action.payload
        ? state.activeChats.find(chat => chat.id === action.payload) || null
        : null;
      return { ...state, selectedChat };
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload].sort(
          (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        )
      };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg =>
          msg.id === action.payload.messageId
            ? { ...msg, ...action.payload.updates }
            : msg
        )
      };
    case 'REMOVE_MESSAGE':
      return {
        ...state,
        messages: state.messages.filter(msg => msg.id !== action.payload)
      };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_TYPING_USERS':
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.chatId]: action.payload.userIds
        }
      };
    case 'SET_ONLINE_USERS':
      return { ...state, onlineUsers: action.payload };
    case 'UPDATE_CHAT_LAST_MESSAGE':
      return {
        ...state,
        activeChats: state.activeChats.map(chat =>
          chat.id === action.payload.chatId
            ? { ...chat, last_message: action.payload.message }
            : chat
        )
      };
    default:
      return state;
  }
}

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider component
export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const { token, isAuthenticated } = useAuth();

  // Get token from auth
  const getToken = useCallback(() => {
    return token ? token.replace('Bearer ', '') : undefined;
  }, [token]);

  // Early return if not authenticated
  if (!isAuthenticated) {
    console.warn('ChatProvider: User not authenticated');
  }

  // Track loading state to prevent duplicate calls
  const isLoadingRef = useRef(false);
  const hasConnectedRef = useRef(false);
  const lastTokenRef = useRef<string | null>(null);
  const lastLoadTimeRef = useRef<number>(0);
  const connectionStatusRef = useRef<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // Internal implementation of loadChats
  const loadChatsImpl = async () => {
    // Prevent duplicate calls if already loading
    if (isLoadingRef.current) {
      console.log('🔄 ChatContext.loadChats already in progress, skipping');
      return;
    }

    // Check if we've loaded chats recently (within 5 seconds)
    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTimeRef.current;
    if (lastLoadTimeRef.current > 0 && timeSinceLastLoad < 5000) {
      return;
    }
    isLoadingRef.current = true;
    lastLoadTimeRef.current = now;
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const token = getToken();

      // Try to load from API first
      if (token) {
        try {
          const response = await chatService.getChats(token);

          if (response.isSuccess && response.data) {
            dispatch({ type: 'SET_ERROR', payload: null }); // Clear any previous errors
            dispatch({ type: 'SET_CHATS', payload: response.data.data });
            return;
          }
        } catch (apiError) {
          console.warn('API failed, falling back to mock data:', apiError);
        }
      }

      // Fallback to mock data for development/testing
      console.log('Using mock chat data for development');
      const userRole = state.userRole;
      const userId = state.currentUser.id || `${userRole}-user`;
      const mockData = getMockChatsForRole(userRole, userId);
      dispatch({ type: 'SET_CHATS', payload: mockData });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load chats';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });

      // Even on error, provide mock data for testing
      const userRole = state.userRole;
      const userId = state.currentUser.id || `${userRole}-user`;
      const mockData = getMockChatsForRole(userRole, userId);
      dispatch({ type: 'SET_CHATS', payload: mockData });
    } finally {
      isLoadingRef.current = false;
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Debounced version of loadChats that prevents multiple calls within 5 seconds
  const loadChats = useCallback(
    debounce(() => {
      loadChatsImpl();
    }, 5000, { leading: true, trailing: false }),
    [getToken]
  );

  // Load messages for a specific chat
  const loadMessages = useCallback(async (chatId: string, page: number = 1) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const token = getToken();

      // Try to load from API first
      if (token) {
        try {
          const response = await chatService.getMessages(chatId, page, token);

          if (response.isSuccess && response.data) {
            if (page === 1) {
              // First page - replace all messages
              dispatch({ type: 'SET_MESSAGES', payload: response.data.data });
            } else {
              // Additional pages - prepend to existing messages
              const currentMessages = state.messages;
              const sortedMessages = [...response.data.data, ...currentMessages].sort(
                (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
              );
              dispatch({ type: 'SET_MESSAGES', payload: sortedMessages });
            }
            return;
          }
        } catch (apiError) {
          console.warn('API failed for messages, falling back to mock data:', apiError);
        }
      }

      // Fallback to mock data
      console.log('Using mock message data for development');
      const mockMessages = getMockMessagesForChat(chatId);
      dispatch({ type: 'SET_MESSAGES', payload: mockMessages });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load messages';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Error loading messages:', error);

      // Even on error, provide mock data for testing
      const mockMessages = getMockMessagesForChat(chatId);
      dispatch({ type: 'SET_MESSAGES', payload: mockMessages });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [getToken, state.messages]);

  // Send message with loading state protection and retry mechanism
  const sendMessage = useCallback(async (chatId: string, messageData: SendMessageRequest) => {
    console.log('ChatContext sendMessage called:', { chatId, messageData });

    // Prevent multiple calls
    if (state.isLoading) {
      console.log('SendMessage blocked - already loading');
      throw new Error('Another message is currently being sent');
    }

    // Set loading state
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    // Validate token
    const token = getToken();
    console.log('Token available:', !!token);
    if (!token) {
      const errorMessage = 'Authentication token is missing';
      console.error('SendMessage failed - no token');
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      throw new Error(errorMessage);
    }

    // Wrap the entire retry mechanism in a timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        console.error('SendMessage operation timed out');
        dispatch({ type: 'SET_LOADING', payload: false }); // Ensure loading state is reset
        reject(new Error('Send message operation timed out after 60 seconds'));
      }, 60000); // 60 second total timeout
    });

    const retryPromise = (async () => {
      // Retry mechanism
      const maxRetries = 3;
      let retries = 0;
      let lastError: Error | null = null;

      while (retries < maxRetries) {
        try {
          const response = await chatService.sendMessage(chatId, messageData, token);

          if (!response.isSuccess || !response.data) {
            throw new Error(response.error || 'Failed to send message');
          }
          // Reset loading state
          dispatch({ type: 'SET_LOADING', payload: false });
          return response;

        } catch (error) {
          lastError = error instanceof Error ? error : new Error('Failed to send message');

          retries++;
          if (retries < maxRetries) {
            const backoffTime = Math.min(1000 * Math.pow(2, retries), 10000);
            await new Promise(resolve => setTimeout(resolve, backoffTime));
          }
        }
      }

      // If we get here, all retries failed
      const errorMessage = lastError?.message || 'Failed to send message after multiple attempts';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      throw new Error(errorMessage);
    })();

    try {
      return await Promise.race([retryPromise, timeoutPromise]);
    } catch (error) {
      // Ensure loading state is always reset
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  }, [getToken, state.isLoading]);

  // Initialize chat for specific role
  const initializeForRole = useCallback((userRole: UserRole, user: ChatParticipant) => {
    dispatch({ type: 'SET_USER_ROLE', payload: userRole });
    dispatch({ type: 'SET_CURRENT_USER', payload: user });
  }, []);

  // Join chat
  const joinChat = useCallback((chatId: string) => {
    const chat = state.activeChats.find(c => c.id === chatId);
    if (chat) {
      dispatch({ type: 'SELECT_CHAT', payload: chatId });
      websocketService.joinChat(chatId);
      loadMessages(chatId);
    }
  }, [loadMessages, state.activeChats]);

  // Leave chat
  const leaveChat = useCallback(() => {
    dispatch({ type: 'SELECT_CHAT', payload: null });
    dispatch({ type: 'SET_MESSAGES', payload: [] });
    websocketService.leaveChat();
  }, []);

  // Create new chat
  const createChat = useCallback(async (participantIds: string[], type: 'direct' | 'group' = 'direct', name?: string) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('Authentication token is missing');
      }

      const response = await chatService.createChat(participantIds.join(','), type, name, token);

      if (response.isSuccess && response.data) {
        dispatch({ type: 'ADD_CHAT', payload: response.data.data });
        return response.data.data;
      } else {
        throw new Error(response.error || 'Failed to create chat');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create chat';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, [getToken]);

  // Update chat settings
  const updateChatSettings = useCallback(async (chatId: string, settings: Partial<Chat>) => {
    try {
      // TODO: Implement chat settings update API call
      dispatch({
        type: 'UPDATE_CHAT',
        payload: { chatId, updates: settings }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update chat settings';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  // Pin/unpin message
  const pinMessage = useCallback(async (chatId: string, messageId: string) => {
    try {
      // TODO: Implement pin message API call
      console.log('Pin message:', chatId, messageId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to pin message';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  const unpinMessage = useCallback(async (chatId: string, messageId: string) => {
    try {
      // TODO: Implement unpin message API call
      console.log('Unpin message:', chatId, messageId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unpin message';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  // Mute/unmute chat
  const muteChat = useCallback(async (chatId: string) => {
    try {
      // TODO: Implement mute chat API call
      dispatch({
        type: 'UPDATE_CHAT',
        payload: { chatId, updates: { /* muted: true */ } }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to mute chat';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  const unmuteChat = useCallback(async (chatId: string) => {
    try {
      // TODO: Implement unmute chat API call
      dispatch({
        type: 'UPDATE_CHAT',
        payload: { chatId, updates: { /* muted: false */ } }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unmute chat';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  // Block/unblock user
  const blockUser = useCallback(async (userId: string) => {
    try {
      // TODO: Implement block user API call
      console.log('Block user:', userId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to block user';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  const unblockUser = useCallback(async (userId: string) => {
    try {
      // TODO: Implement unblock user API call
      console.log('Unblock user:', userId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unblock user';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  // Report message
  const reportMessage = useCallback(async (chatId: string, messageId: string, reason: string) => {
    try {
      // TODO: Implement report message API call
      console.log('Report message:', chatId, messageId, reason);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to report message';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  }, []);

  // Mark as read
  const markAsRead = useCallback(async (chatId: string) => {
    try {
      await chatService.markAsRead(chatId, getToken());
    } catch (error) {
      console.error('Error marking chat as read:', error);
    }
  }, [getToken]);

  // Delete message
  const deleteMessage = useCallback(async (chatId: string, messageId: string) => {
    try {
      const response = await chatService.deleteMessage(chatId, messageId, getToken());

      if (response.isSuccess) {
        // Remove message from local state
        const currentMessages = state.messages;
        dispatch({
          type: 'SET_MESSAGES',
          payload: currentMessages.filter(msg => msg.id !== messageId)
        });
      } else {
        throw new Error(response.error || 'Failed to delete message');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete message';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Error deleting message:', error);
      throw error;
    }
  }, [getToken]);

  // WebSocket connection management
  const connect = useCallback(() => {
    // Prevent multiple connection attempts
    if (connectionStatusRef.current === 'connecting' || connectionStatusRef.current === 'connected') {
      return;
    }
    connectionStatusRef.current = 'connecting';

    websocketService.connect()
      .then(() => {
        connectionStatusRef.current = 'connected';
        console.log('WebSocket connected successfully');
      })
      .catch(error => {
        connectionStatusRef.current = 'disconnected';
        console.warn('WebSocket connection failed, continuing with HTTP-only mode:', error);
        // Don't show error to user - WebSocket is optional for basic functionality
      });
  }, []);

  const disconnect = useCallback(() => {
    websocketService.disconnect();
    connectionStatusRef.current = 'disconnected';
  }, []);

  const reconnect = useCallback(() => {
    connectionStatusRef.current = 'connecting';

    websocketService.reconnect();
  }, []);

  // Combined WebSocket setup and connection management
  useEffect(() => {
    // Only proceed if authenticated
    if (!isAuthenticated || !token) {
      return;
    }

    // Track if this effect instance has connected
    const effectInstanceId = Date.now();

    // Connect to WebSocket if not already connected or connecting
    if (connectionStatusRef.current === 'disconnected') {
      connect();
    }

    // Set up event listeners
    const unsubscribeMessage = websocketService.onMessage((message: WebSocketMessage) => {
      if (message.action === 'message_sent') {
        const messageData = (message as MessageSentMessage).data;
        dispatch({ type: 'ADD_MESSAGE', payload: messageData });

        // Update chat's last message
        dispatch({ 
          type: 'UPDATE_CHAT_LAST_MESSAGE', 
          payload: { chatId: messageData.chat_id, message: messageData }
        });
      }
    });

    const unsubscribeConnect = websocketService.onConnect(() => {
      connectionStatusRef.current = 'connected';
      dispatch({ type: 'SET_CONNECTED', payload: true });
    });

    const unsubscribeDisconnect = websocketService.onDisconnect(() => {
      connectionStatusRef.current = 'disconnected';
      dispatch({ type: 'SET_CONNECTED', payload: false });
    });

    const unsubscribeError = websocketService.onError((error) => {
      // dispatch({ type: 'SET_ERROR', payload: 'WebSocket connection error' });
    });

    // Load chats immediately when authenticated and token changes
    if (token && token !== lastTokenRef.current) {
      lastTokenRef.current = token;
      loadChats();
    }

    // Cleanup function
    return () => {
      // Unsubscribe from all event listeners
      unsubscribeMessage();
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();

      // Clear all listeners from the service
      websocketService.clearAllListeners();

      // Only disconnect if we're connected or connecting
      if (connectionStatusRef.current !== 'disconnected') {
        console.log(`WebSocket effect instance ${effectInstanceId} disconnecting from ${connectionStatusRef.current} state`);
        disconnect();
      }
    };
  }, [isAuthenticated, token, connect, disconnect, loadChats]); // Include all dependencies

  const contextValue: ChatContextType = useMemo(() => ({
    // New universal chat state and dispatch
    state,
    dispatch,

    // Legacy compatibility - map new state to old interface
    chats: state.activeChats,
    currentChat: state.selectedChat,
    messages: state.messages,
    isLoading: state.isLoading,
    isConnected: state.isConnected,
    error: state.error,

    // Existing methods
    loadChats,
    loadMessages,
    sendMessage,
    joinChat,
    leaveChat,
    markAsRead,
    deleteMessage,
    connect,
    disconnect,
    reconnect,

    // New universal chat methods
    initializeForRole,
    createChat,
    updateChatSettings,
    pinMessage,
    unpinMessage,
    muteChat,
    unmuteChat,
    blockUser,
    unblockUser,
    reportMessage,
  }), [
    state,
    dispatch,
    loadChats,
    loadMessages,
    sendMessage,
    joinChat,
    leaveChat,
    markAsRead,
    deleteMessage,
    connect,
    disconnect,
    reconnect,
    initializeForRole,
    createChat,
    updateChatSettings,
    pinMessage,
    unpinMessage,
    muteChat,
    unmuteChat,
    blockUser,
    unblockUser,
    reportMessage,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook to use chat context
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
